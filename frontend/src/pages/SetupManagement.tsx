import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  TextField,
  Button,
  Card,
  CardContent,
  CardActions,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Alert,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Computer as ComputerIcon,
  Storage as StorageIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';

interface Server {
  id?: number;
  name: string;
  hostname: string;
  ip_address: string;
  server_type: 'sut' | 'aux';
  username: string;
  password: string;
  setup_id?: number;
  is_active?: boolean;
  detected_esxi_version?: string;
  esxi_version_status?: 'unknown' | 'checking' | 'match' | 'mismatch' | 'error';
  esxi_version_checked_at?: string;
  selectedServerId?: number; // For tracking which existing server is selected
}

interface Agent {
  id?: number;
  name: string;
  ip_address: string;
  port: number;
  username: string;
  password: string;
  is_primary: boolean;
  setup_id?: number;
}

interface Setup {
  id?: number;
  name: string;
  description: string;
  esxi_version?: '8.0' | '9.0';
  servers: Server[];
  agents: Agent[];
}

export default function SetupManagement() {
  const [open, setOpen] = useState(false);
  const [editingSetup, setEditingSetup] = useState<Setup | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [setupToDelete, setSetupToDelete] = useState<Setup | null>(null);
  const [deleteConfirmText, setDeleteConfirmText] = useState('');
  const [deleteServersAlso, setDeleteServersAlso] = useState(false);
  const [createError, setCreateError] = useState<string>('');
  const [setupForm, setSetupForm] = useState<Setup>({
    name: '',
    description: '',
    esxi_version: undefined,
    servers: [],
    agents: [],
  });

  const queryClient = useQueryClient();

  const { data: setups = [] } = useQuery<Setup[]>({
    queryKey: ['setups'],
    queryFn: async () => {
      const response = await axios.get('/api/v1/setups/');
      return response.data;
    },
  });

  // Fetch all servers for the dropdown
  const { data: allServers = [] } = useQuery<Server[]>({
    queryKey: ['all-servers'],
    queryFn: async () => {
      const response = await axios.get('/api/v1/servers/');
      return response.data;
    },
  });

  const createSetupMutation = useMutation({
    mutationFn: async (setup: Setup) => {
      const response = await axios.post('/api/v1/setups/', {
        name: setup.name,
        description: setup.description,
        esxi_version: setup.esxi_version,
      });
      const setupId = response.data.id;

      // Assign existing servers to the setup
      if (setup.servers.length > 0) {
        for (const server of setup.servers) {
          if (server.selectedServerId) {
            // Update the existing server to assign it to this setup
            await axios.put(`/api/v1/servers/${server.selectedServerId}`, {
              setup_id: setupId,
              server_type: server.server_type
            });
          }
        }
      }

      // Create agents
      if (setup.agents.length > 0) {
        await axios.post(`/api/v1/setups/${setupId}/agents`, setup.agents);
      }

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['setups'] });
      setOpen(false);
      resetForm();
      setCreateError('');
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.detail || 'Failed to create setup';
      setCreateError(errorMessage);
    },
  });

  const updateSetupMutation = useMutation({
    mutationFn: async (setup: Setup) => {
      if (!setup.id) throw new Error('Setup ID is required for update');

      // Update setup basic info
      const response = await axios.put(`/api/v1/setups/${setup.id}`, {
        name: setup.name,
        description: setup.description,
        esxi_version: setup.esxi_version,
      });

      // For now, we'll just return the response
      // TODO: Implement server and agent updates
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['setups'] });
      setOpen(false);
      resetForm();
    },
  });

  const deleteSetupMutation = useMutation({
    mutationFn: async ({ setupId, deleteServers }: { setupId: number; deleteServers: boolean }) => {
      console.log(`Attempting to delete setup ${setupId}, deleteServers: ${deleteServers}`);
      const response = await axios.delete(`/api/v1/setups/${setupId}?delete_servers=${deleteServers}`);
      console.log('Delete response:', response.data);
      return response.data;
    },
    onSuccess: (data, setupId) => {
      console.log(`Setup ${setupId} deleted successfully:`, data);
      queryClient.invalidateQueries({ queryKey: ['setups'] });
    },
    onError: (error, setupId) => {
      console.error(`Failed to delete setup ${setupId}:`, error);
    },
  });

  const resetForm = () => {
    setSetupForm({
      name: '',
      description: '',
      esxi_version: undefined,
      servers: [],
      agents: [],
    });
    setEditingSetup(null);
    setCreateError('');
  };

  const handleOpenDialog = async (setup?: Setup) => {
    if (setup) {
      setEditingSetup(setup);

      // Fetch full setup details including servers and agents
      try {
        const response = await axios.get(`/api/v1/setups/${setup.id}`);
        const fullSetup = response.data;

        // Transform servers to include selectedServerId for the dropdown
        const transformedServers = (fullSetup.servers || []).map((server: any) => ({
          ...server,
          selectedServerId: server.id, // Set selectedServerId to the server's own ID
        }));

        // Ensure servers and agents arrays exist
        setSetupForm({
          ...fullSetup,
          servers: transformedServers,
          agents: fullSetup.agents || [],
        });
      } catch (error) {
        console.error('Failed to fetch setup details:', error);
        // Fallback to basic setup data
        setSetupForm({
          ...setup,
          servers: [],
          agents: [],
        });
      }
    } else {
      resetForm();
    }
    setOpen(true);
  };

  const handleCloseDialog = () => {
    setOpen(false);
    resetForm();
  };

  const handleSubmit = () => {
    if (editingSetup) {
      updateSetupMutation.mutate(setupForm);
    } else {
      createSetupMutation.mutate(setupForm);
    }
  };

  const addServerAssignment = () => {
    setSetupForm({
      ...setupForm,
      servers: [
        ...setupForm.servers,
        {
          id: undefined, // Will be set when server is selected
          name: '',
          hostname: '',
          ip_address: '',
          server_type: 'sut',
          username: 'root',
          password: 'ca$hc0w',
          selectedServerId: undefined, // New field for tracking selected server
        },
      ],
    });
  };

  const updateServer = (index: number, server: Server) => {
    const newServers = [...setupForm.servers];
    newServers[index] = server;
    setSetupForm({ ...setupForm, servers: newServers });
  };

  const removeServer = (index: number) => {
    const newServers = setupForm.servers.filter((_, i) => i !== index);
    setSetupForm({ ...setupForm, servers: newServers });
  };

  const addAgent = () => {
    setSetupForm({
      ...setupForm,
      agents: [
        ...setupForm.agents,
        {
          name: '',
          ip_address: '',
          port: 22,
          username: 'root',
          password: 'vmware',
          is_primary: setupForm.agents.length === 0,
        },
      ],
    });
  };

  const updateAgent = (index: number, agent: Agent) => {
    const newAgents = [...setupForm.agents];
    newAgents[index] = agent;
    setSetupForm({ ...setupForm, agents: newAgents });
  };

  const removeAgent = (index: number) => {
    const newAgents = setupForm.agents.filter((_, i) => i !== index);
    setSetupForm({ ...setupForm, agents: newAgents });
  };

  const handleServerSelection = (index: number, serverId: number | '') => {
    const newServers = [...setupForm.servers];
    if (serverId === '') {
      // Clear selection
      newServers[index] = {
        ...newServers[index],
        selectedServerId: undefined,
        id: undefined,
        name: '',
        hostname: '',
        ip_address: '',
      };
    } else {
      // Find the selected server
      const selectedServer = allServers.find(s => s.id === serverId);
      if (selectedServer) {
        // Auto-assign server type based on existing assignments
        let autoServerType = selectedServer.server_type;

        // Check if we already have a server of this type assigned
        const existingServerTypes = newServers
          .filter((s, i) => i !== index && s.selectedServerId)
          .map(s => s.server_type);

        // If we already have a SUT and this is a SUT, make it AUX
        if (autoServerType === 'sut' && existingServerTypes.includes('sut')) {
          autoServerType = 'aux';
        }
        // If we already have an AUX and this is an AUX, make it SUT
        else if (autoServerType === 'aux' && existingServerTypes.includes('aux')) {
          autoServerType = 'sut';
        }

        newServers[index] = {
          ...newServers[index],
          selectedServerId: serverId,
          id: selectedServer.id,
          name: selectedServer.name,
          hostname: selectedServer.hostname,
          ip_address: selectedServer.ip_address,
          username: selectedServer.username,
          password: selectedServer.password,
          server_type: autoServerType,
        };
      }
    }
    setSetupForm({ ...setupForm, servers: newServers });
  };

  const getServerConflictWarning = (serverId: number) => {
    const server = allServers.find(s => s.id === serverId);
    if (server?.setup_id && server.setup_id !== setupForm.id) {
      const conflictSetup = setups.find(s => s.id === server.setup_id);
      return `Warning: This server is already assigned to setup "${conflictSetup?.name || server.setup_id}"`;
    }
    return null;
  };

  const getAvailableServers = () => {
    // Get servers that are not already selected in this form
    const selectedServerIds = setupForm.servers
      .map(s => s.selectedServerId)
      .filter(id => id !== undefined);

    return allServers.filter(server =>
      !server.id || !selectedServerIds.includes(server.id) ||
      setupForm.servers.some(s => s.selectedServerId === server.id)
    );
  };

  const generateShelfSetup = () => {
    const shelfNumber = prompt('Enter shelf number (e.g., 65):');
    if (!shelfNumber) return;

    const esxiVersion = prompt('Enter ESXi version (8.0 or 9.0):');
    if (!esxiVersion || !['8.0', '9.0'].includes(esxiVersion)) {
      alert('Please enter a valid ESXi version (8.0 or 9.0)');
      return;
    }

    setSetupForm({
      name: `Shelf${shelfNumber}`,
      description: `VMware NIC certification test environment for Shelf ${shelfNumber}`,
      esxi_version: esxiVersion as '8.0' | '9.0',
      servers: [
        {
          name: `shelf${shelfNumber}sut`,
          hostname: `shelf${shelfNumber}sut.esxcert.local`,
          ip_address: `172.101.${shelfNumber}.11`,
          server_type: 'sut',
          username: 'root',
          password: 'ca$hc0w',
        },
        {
          name: `shelf${shelfNumber}aux`,
          hostname: `shelf${shelfNumber}aux.esxcert.local`,
          ip_address: `172.101.${shelfNumber}.12`,
          server_type: 'aux',
          username: 'root',
          password: 'ca$hc0w',
        },
      ],
      agents: [
        {
          name: `shelf${shelfNumber}sut`,
          ip_address: '',
          port: 22,
          username: 'root',
          password: 'vmware',
          is_primary: true,
        },
        {
          name: `shelf${shelfNumber}aux`,
          ip_address: '',
          port: 22,
          username: 'root',
          password: 'vmware',
          is_primary: false,
        },
      ],
    });
    setOpen(true);
  };

  const handleDeleteClick = (setup: Setup) => {
    setSetupToDelete(setup);
    setDeleteConfirmText('');
    setDeleteServersAlso(false);
    setDeleteConfirmOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (setupToDelete?.id && deleteConfirmText === 'DELETE') {
      deleteSetupMutation.mutate({
        setupId: setupToDelete.id,
        deleteServers: deleteServersAlso
      });
      setDeleteConfirmOpen(false);
      setSetupToDelete(null);
      setDeleteConfirmText('');
      setDeleteServersAlso(false);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirmOpen(false);
    setSetupToDelete(null);
    setDeleteConfirmText('');
    setDeleteServersAlso(false);
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4">Setup Management</Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<ComputerIcon />}
            onClick={generateShelfSetup}
            sx={{ mr: 1 }}
          >
            Generate Shelf Setup
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Create Setup
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {setups.map((setup) => (
          <Grid item xs={12} md={6} lg={4} key={setup.id}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  {setup.name}
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {setup.description}
                </Typography>
                <Box sx={{ mt: 2 }}>
                  <Chip
                    icon={<StorageIcon />}
                    label={`${setup.servers?.length || 0} Servers`}
                    size="small"
                    sx={{ mr: 1, mb: 1 }}
                  />
                  <Chip
                    icon={<ComputerIcon />}
                    label={`${setup.agents?.length || 0} Agents`}
                    size="small"
                    sx={{ mr: 1, mb: 1 }}
                  />
                  {setup.esxi_version && (
                    <Chip
                      label={`ESXi ${setup.esxi_version}`}
                      color="primary"
                      variant="outlined"
                      size="small"
                      sx={{ mb: 1 }}
                    />
                  )}
                </Box>
              </CardContent>
              <CardActions>
                <Button
                  size="small"
                  startIcon={<EditIcon />}
                  onClick={() => handleOpenDialog(setup)}
                >
                  Edit
                </Button>
                <Button
                  size="small"
                  color="error"
                  startIcon={<DeleteIcon />}
                  onClick={() => handleDeleteClick(setup)}
                  disabled={deleteSetupMutation.isPending}
                >
                  Delete
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Create/Edit Setup Dialog */}
      <Dialog open={open} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingSetup ? 'Edit Setup' : 'Create New Setup'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            {createError && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {createError}
              </Alert>
            )}
            <TextField
              fullWidth
              label="Setup Name"
              value={setupForm.name}
              onChange={(e) => setSetupForm({ ...setupForm, name: e.target.value })}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Description"
              multiline
              rows={2}
              value={setupForm.description}
              onChange={(e) => setSetupForm({ ...setupForm, description: e.target.value })}
              sx={{ mb: 2 }}
            />
            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel>ESXi Version</InputLabel>
              <Select
                value={setupForm.esxi_version || ''}
                label="ESXi Version"
                onChange={(e) => setSetupForm({ ...setupForm, esxi_version: e.target.value as '8.0' | '9.0' | undefined })}
              >
                <MenuItem value="">
                  <em>Select ESXi version...</em>
                </MenuItem>
                <MenuItem value="8.0">ESXi 8.0</MenuItem>
                <MenuItem value="9.0">ESXi 9.0</MenuItem>
              </Select>
            </FormControl>

            {/* Servers Section */}
            <Typography variant="h6" gutterBottom>
              Server Assignments
              <Button
                size="small"
                startIcon={<AddIcon />}
                onClick={addServerAssignment}
                sx={{ ml: 2 }}
              >
                Assign Server
              </Button>
            </Typography>
            {setupForm.servers.map((server, index) => {
              const availableServers = getAvailableServers();
              const conflictWarning = server.selectedServerId ? getServerConflictWarning(server.selectedServerId) : null;

              return (
                <Paper key={index} sx={{ p: 2, mb: 2 }}>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} sm={6} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Select Server</InputLabel>
                        <Select
                          value={server.selectedServerId || ''}
                          label="Select Server"
                          onChange={(e) => handleServerSelection(index, e.target.value as number | '')}
                        >
                          <MenuItem value="">
                            <em>Select a server...</em>
                          </MenuItem>
                          {availableServers.map((availableServer) => (
                            <MenuItem key={availableServer.id} value={availableServer.id}>
                              {availableServer.name} ({availableServer.ip_address}) - {availableServer.server_type.toUpperCase()}
                              {availableServer.setup_id && availableServer.setup_id !== setupForm.id && (
                                <span style={{ color: 'orange', marginLeft: 8 }}>
                                  [Assigned to other setup]
                                </span>
                              )}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <FormControl fullWidth>
                        <InputLabel>Role</InputLabel>
                        <Select
                          value={server.server_type}
                          label="Role"
                          onChange={(e) => updateServer(index, { ...server, server_type: e.target.value as 'sut' | 'aux' })}
                          disabled={!server.selectedServerId}
                        >
                          <MenuItem value="sut">SUT (System Under Test)</MenuItem>
                          <MenuItem value="aux">AUX (Auxiliary)</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={12} md={3} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                      <IconButton onClick={() => removeServer(index)} color="error">
                        <DeleteIcon />
                      </IconButton>
                    </Grid>
                    {conflictWarning && (
                      <Grid item xs={12}>
                        <Alert severity="warning" sx={{ mt: 1 }}>
                          {conflictWarning}
                        </Alert>
                      </Grid>
                    )}
                    {server.selectedServerId && (
                      <Grid item xs={12}>
                        <Box sx={{ mt: 1, p: 1, bgcolor: 'primary.50', border: '1px solid', borderColor: 'primary.200', borderRadius: 1 }}>
                          <Typography variant="body2" color="primary.main">
                            <strong>Server Details:</strong> {server.name} | {server.hostname} | {server.ip_address} | {server.username}
                          </Typography>
                        </Box>
                      </Grid>
                    )}
                  </Grid>
                </Paper>
              );
            })}

            {/* Agents Section */}
            <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
              Agents
              <Button
                size="small"
                startIcon={<AddIcon />}
                onClick={addAgent}
                sx={{ ml: 2 }}
              >
                Add Agent
              </Button>
            </Typography>
            {setupForm.agents.map((agent, index) => (
              <Paper key={index} sx={{ p: 2, mb: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={3}>
                    <TextField
                      fullWidth
                      label="Name"
                      value={agent.name}
                      onChange={(e) => updateAgent(index, { ...agent, name: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={3}>
                    <TextField
                      fullWidth
                      label="IP Address"
                      value={agent.ip_address}
                      onChange={(e) => updateAgent(index, { ...agent, ip_address: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={2}>
                    <TextField
                      fullWidth
                      label="Port"
                      type="number"
                      value={agent.port}
                      onChange={(e) => updateAgent(index, { ...agent, port: parseInt(e.target.value) })}
                    />
                  </Grid>
                  <Grid item xs={2}>
                    <TextField
                      fullWidth
                      label="Username"
                      value={agent.username}
                      onChange={(e) => updateAgent(index, { ...agent, username: e.target.value })}
                    />
                  </Grid>
                  <Grid item xs={1}>
                    <IconButton onClick={() => removeAgent(index)} color="error">
                      <DeleteIcon />
                    </IconButton>
                  </Grid>
                </Grid>
              </Paper>
            ))}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={!setupForm.name || createSetupMutation.isPending}
          >
            {editingSetup ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onClose={handleDeleteCancel} maxWidth="sm" fullWidth>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the setup "{setupToDelete?.name}"? This action cannot be undone.
          </DialogContentText>
          <FormControlLabel
            control={
              <Checkbox
                checked={deleteServersAlso}
                onChange={(e) => setDeleteServersAlso(e.target.checked)}
                color="error"
              />
            }
            label="Also delete assigned servers permanently"
            sx={{ mt: 2, mb: 1 }}
          />
          <DialogContentText variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {deleteServersAlso
              ? "⚠️ This will permanently delete the setup AND all its assigned servers"
              : "ℹ️ This will delete the setup but keep servers (they will be unassigned)"
            }
          </DialogContentText>
          <DialogContentText sx={{ mt: 1, fontWeight: 'bold' }}>
            To confirm, please type "DELETE" in the field below:
          </DialogContentText>
          <TextField
            fullWidth
            value={deleteConfirmText}
            onChange={(e) => setDeleteConfirmText(e.target.value)}
            placeholder="Type DELETE to confirm"
            sx={{ mt: 1 }}
            autoFocus
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={deleteConfirmText !== 'DELETE' || deleteSetupMutation.isPending}
          >
            Delete Setup
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
