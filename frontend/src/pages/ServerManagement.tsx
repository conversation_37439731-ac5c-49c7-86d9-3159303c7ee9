import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Paper,
  <PERSON>ton,
  Card,
  CardContent,
  CardActions,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  DialogContentText,
  Alert,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Storage as StorageIcon,
  Computer as ComputerIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';

interface Setup {
  id: number;
  name: string;
  description: string;
  esxi_version?: '8.0' | '9.0';
}

interface Server {
  id?: number;
  name: string;
  hostname: string;
  ip_address: string;
  server_type: 'sut' | 'aux';
  username: string;
  password: string;
  setup_id?: number;
  is_active: boolean;
  detected_esxi_version?: string;
  esxi_version_status?: 'unknown' | 'checking' | 'match' | 'mismatch' | 'error';
  esxi_version_checked_at?: string;
  created_at?: string;
  setup?: Setup;
}

export default function ServerManagement() {
  const [open, setOpen] = useState(false);
  const [editingServer, setEditingServer] = useState<Server | null>(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [serverToDelete, setServerToDelete] = useState<Server | null>(null);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [serverForm, setServerForm] = useState<Server>({
    name: '',
    hostname: '',
    ip_address: '',
    server_type: 'sut',
    username: 'root',
    password: 'ca$hc0w',
    setup_id: undefined,
    is_active: true,
  });

  const queryClient = useQueryClient();

  // Fetch servers
  const { data: servers = [] } = useQuery<Server[]>({
    queryKey: ['servers'],
    queryFn: async () => {
      const response = await axios.get('/api/v1/servers/');
      return response.data;
    },
  });

  // Fetch setups for the dropdown
  const { data: setups = [] } = useQuery<Setup[]>({
    queryKey: ['setups'],
    queryFn: async () => {
      const response = await axios.get('/api/v1/setups/');
      return response.data;
    },
  });

  // Create server mutation
  const createServerMutation = useMutation({
    mutationFn: async (server: Server) => {
      const response = await axios.post('/api/v1/servers/', server);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['servers'] });
      setOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      if (error.response?.data?.detail?.errors) {
        setValidationErrors(error.response.data.detail.errors);
      } else {
        setValidationErrors([error.response?.data?.detail || 'Failed to create server']);
      }
    },
  });

  // Update server mutation
  const updateServerMutation = useMutation({
    mutationFn: async (server: Server) => {
      if (!server.id) throw new Error('Server ID is required for update');
      const response = await axios.put(`/api/v1/servers/${server.id}`, server);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['servers'] });
      setOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      if (error.response?.data?.detail?.errors) {
        setValidationErrors(error.response.data.detail.errors);
      } else {
        setValidationErrors([error.response?.data?.detail || 'Failed to update server']);
      }
    },
  });

  // Delete server mutation
  const deleteServerMutation = useMutation({
    mutationFn: async (serverId: number) => {
      const response = await axios.delete(`/api/v1/servers/${serverId}`);
      return response.data;
    },
    onSuccess: (data, serverId) => {
      console.log(`Server ${serverId} deleted successfully:`, data);
      queryClient.invalidateQueries({ queryKey: ['servers'] });
    },
    onError: (error, serverId) => {
      console.error(`Failed to delete server ${serverId}:`, error);
    },
  });

  // Check ESXi version mutation
  const checkEsxiVersionMutation = useMutation({
    mutationFn: async (serverId: number) => {
      const response = await axios.post(`/api/v1/servers/${serverId}/check-esxi-version`);
      return response.data;
    },
    onSuccess: (data, serverId) => {
      console.log(`ESXi version check completed for server ${serverId}:`, data);
      // Refresh servers to get updated status
      queryClient.invalidateQueries({ queryKey: ['servers'] });
    },
    onError: (error, serverId) => {
      console.error(`Failed to check ESXi version for server ${serverId}:`, error);
    },
  });

  const resetForm = () => {
    setServerForm({
      name: '',
      hostname: '',
      ip_address: '',
      server_type: 'sut',
      username: 'root',
      password: 'ca$hc0w',
      setup_id: undefined,
      is_active: true,
    });
    setEditingServer(null);
    setValidationErrors([]);
  };

  const getEsxiVersionStatusColor = (status?: string) => {
    switch (status) {
      case 'match':
        return 'success';
      case 'mismatch':
        return 'error';
      case 'checking':
        return 'warning';
      case 'error':
        return 'error';
      case 'unknown':
        return 'info';
      default:
        return 'info';
    }
  };

  const getEsxiVersionStatusText = (status?: string, detectedVersion?: string) => {
    switch (status) {
      case 'match':
        return `ESXi ${detectedVersion} ✓`;
      case 'mismatch':
        return `ESXi ${detectedVersion} ⚠️`;
      case 'checking':
        return 'Checking...';
      case 'error':
        return 'Check failed';
      case 'unknown':
        return detectedVersion ? `ESXi ${detectedVersion}` : 'Unknown';
      default:
        return detectedVersion ? `ESXi ${detectedVersion}` : 'Unknown';
    }
  };

  const getServerCardColor = (server: Server) => {
    if (!server.setup_id) return 'default';

    const setup = setups.find(s => s.id === server.setup_id);
    if (!setup?.esxi_version || !server.esxi_version_status) return 'default';

    if (server.esxi_version_status === 'mismatch') {
      return '#ffebee'; // Light red background
    } else if (server.esxi_version_status === 'match') {
      return '#e8f5e8'; // Light green background
    }

    return 'default';
  };

  const validateServer = (server: Server, allServers: Server[]) => {
    const errors: string[] = [];

    // Check for duplicate IP address
    const duplicateIP = allServers.find(s =>
      s.ip_address === server.ip_address && s.id !== server.id
    );
    if (duplicateIP) {
      errors.push(`IP address ${server.ip_address} is already used by server "${duplicateIP.name}"`);
    }

    // Check for duplicate hostname
    const duplicateHostname = allServers.find(s =>
      s.hostname === server.hostname && s.id !== server.id
    );
    if (duplicateHostname) {
      errors.push(`Hostname ${server.hostname} is already used by server "${duplicateHostname.name}"`);
    }

    // Validate IP format and shelf conflicts
    if (server.ip_address.startsWith('172.101.')) {
      try {
        const parts = server.ip_address.split('.');
        const shelfNumber = parts[2];
        const hostNumber = parts[3];

        // Check for shelf conflicts
        const shelfServers = allServers.filter(s =>
          s.ip_address.startsWith(`172.101.${shelfNumber}.`) &&
          s.server_type === server.server_type &&
          s.id !== server.id
        );

        if (shelfServers.length > 0) {
          errors.push(`Shelf ${shelfNumber} already has a ${server.server_type.toUpperCase()} server: ${shelfServers[0].name}`);
        }

        // Validate IP pattern for shelf servers
        const expectedHost = server.server_type === 'sut' ? '11' : '12';
        if (hostNumber !== expectedHost) {
          errors.push(`Invalid IP for ${server.server_type.toUpperCase()} server. Expected 172.101.${shelfNumber}.${expectedHost}, got ${server.ip_address}`);
        }
      } catch (error) {
        errors.push(`Invalid IP address format. Expected 172.101.{shelf}.{host}, got ${server.ip_address}`);
      }
    }

    return errors;
  };

  const handleOpenDialog = (server?: Server) => {
    if (server) {
      setEditingServer(server);
      setServerForm(server);
    } else {
      resetForm();
    }
    setOpen(true);
  };

  const handleCloseDialog = () => {
    setOpen(false);
    resetForm();
  };

  const handleSubmit = () => {
    // Validate form
    const errors = validateServer(serverForm, servers || []);
    setValidationErrors(errors);

    if (errors.length > 0) {
      return; // Don't submit if there are validation errors
    }

    if (editingServer) {
      updateServerMutation.mutate(serverForm);
    } else {
      createServerMutation.mutate(serverForm);
    }
  };

  const handleDeleteClick = (server: Server) => {
    setServerToDelete(server);
    setDeleteConfirmOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (serverToDelete?.id) {
      deleteServerMutation.mutate(serverToDelete.id);
    }
    setDeleteConfirmOpen(false);
    setServerToDelete(null);
  };

  const handleDeleteCancel = () => {
    setDeleteConfirmOpen(false);
    setServerToDelete(null);
  };

  const generateShelfServers = () => {
    const shelfNumber = prompt('Enter shelf number (e.g., 65):');
    if (!shelfNumber) return;

    // Create SUT server
    const sutServer = {
      name: `shelf${shelfNumber}sut`,
      hostname: `shelf${shelfNumber}sut.esxcert.local`,
      ip_address: `172.101.${shelfNumber}.11`,
      server_type: 'sut' as const,
      username: 'root',
      password: 'ca$hc0w',
      is_active: true,
    };

    // Create AUX server
    const auxServer = {
      name: `shelf${shelfNumber}aux`,
      hostname: `shelf${shelfNumber}aux.esxcert.local`,
      ip_address: `172.101.${shelfNumber}.12`,
      server_type: 'aux' as const,
      username: 'root',
      password: 'ca$hc0w',
      is_active: true,
    };

    // Create both servers
    createServerMutation.mutate(sutServer);
    setTimeout(() => {
      createServerMutation.mutate(auxServer);
    }, 500);
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4">Server Management</Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<ComputerIcon />}
            onClick={generateShelfServers}
            sx={{ mr: 1 }}
          >
            Generate Shelf Servers
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Add Server
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {servers.map((server) => {
          console.log('Server data:', server); // Debug log
          const cardBgColor = getServerCardColor(server);
          const setup = setups.find(s => s.id === server.setup_id);

          return (
            <Grid item xs={12} md={6} lg={4} key={server.id}>
              <Card sx={{ backgroundColor: cardBgColor }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <StorageIcon sx={{ mr: 1 }} />
                    <Typography variant="h6">
                      {server.name}
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {server.hostname}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    IP: {server.ip_address}
                  </Typography>
                  <Box sx={{ mt: 2 }}>
                    <Chip
                      label={server.server_type.toUpperCase()}
                      color={server.server_type === 'sut' ? 'primary' : 'secondary'}
                      size="small"
                      sx={{ mr: 1, mb: 1 }}
                    />
                    <Chip
                      label={server.is_active ? 'Active' : 'Inactive'}
                      color={server.is_active ? 'success' : 'error'}
                      size="small"
                      sx={{ mr: 1, mb: 1 }}
                    />
                    {server.setup_id && (
                      <Chip
                        label={`Setup: ${setup?.name || server.setup_id}`}
                        variant="outlined"
                        size="small"
                        sx={{ mr: 1, mb: 1 }}
                      />
                    )}
                    {server.detected_esxi_version && (
                      <Chip
                        label={getEsxiVersionStatusText(server.esxi_version_status, server.detected_esxi_version)}
                        color={getEsxiVersionStatusColor(server.esxi_version_status) as any}
                        size="small"
                        sx={{ mb: 1 }}
                      />
                    )}
                    {setup?.esxi_version && server.esxi_version_status === 'mismatch' && (
                      <Chip
                        label={`Expected: ESXi ${setup.esxi_version}`}
                        color="warning"
                        variant="outlined"
                        size="small"
                        sx={{ mb: 1 }}
                      />
                    )}
                  </Box>
                </CardContent>
                <CardActions>
                  <Button
                    size="small"
                    startIcon={<EditIcon />}
                    onClick={() => handleOpenDialog(server)}
                  >
                    Edit
                  </Button>
                  <Button
                    size="small"
                    color="error"
                    startIcon={<DeleteIcon />}
                    onClick={() => handleDeleteClick(server)}
                    disabled={deleteServerMutation.isPending}
                  >
                    Delete
                  </Button>
                  {server.id && (
                    <Button
                      size="small"
                      color="primary"
                      onClick={() => checkEsxiVersionMutation.mutate(server.id!)}
                      disabled={checkEsxiVersionMutation.isPending}
                    >
                      Check ESXi
                    </Button>
                  )}
                </CardActions>
              </Card>
            </Grid>
          );
        })}
      </Grid>

      {/* Create/Edit Server Dialog */}
      <Dialog open={open} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingServer ? 'Edit Server' : 'Create New Server'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            {validationErrors.length > 0 && (
              <Alert severity="error" sx={{ mb: 2 }}>
                <Typography variant="body2" component="div">
                  <strong>Validation Errors:</strong>
                  <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                    {validationErrors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </Typography>
              </Alert>
            )}
            <TextField
              fullWidth
              label="Server Name"
              value={serverForm.name}
              onChange={(e) => setServerForm({ ...serverForm, name: e.target.value })}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Hostname"
              value={serverForm.hostname}
              onChange={(e) => setServerForm({ ...serverForm, hostname: e.target.value })}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="IP Address"
              value={serverForm.ip_address}
              onChange={(e) => setServerForm({ ...serverForm, ip_address: e.target.value })}
              sx={{ mb: 2 }}
              placeholder="e.g., *************"
            />
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Server Type</InputLabel>
              <Select
                value={serverForm.server_type}
                label="Server Type"
                onChange={(e) => setServerForm({ ...serverForm, server_type: e.target.value as 'sut' | 'aux' })}
              >
                <MenuItem value="sut">SUT (System Under Test)</MenuItem>
                <MenuItem value="aux">AUX (Auxiliary)</MenuItem>
              </Select>
            </FormControl>
            <TextField
              fullWidth
              label="Username"
              value={serverForm.username}
              onChange={(e) => setServerForm({ ...serverForm, username: e.target.value })}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Password"
              type="password"
              value={serverForm.password}
              onChange={(e) => setServerForm({ ...serverForm, password: e.target.value })}
              sx={{ mb: 2 }}
            />
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Assign to Setup (Optional)</InputLabel>
              <Select
                value={serverForm.setup_id || ''}
                label="Assign to Setup (Optional)"
                onChange={(e) => setServerForm({ ...serverForm, setup_id: e.target.value ? Number(e.target.value) : undefined })}
              >
                <MenuItem value="">
                  <em>No Setup</em>
                </MenuItem>
                {setups.map((setup) => (
                  <MenuItem key={setup.id} value={setup.id}>
                    {setup.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={serverForm.is_active ? 'active' : 'inactive'}
                label="Status"
                onChange={(e) => setServerForm({ ...serverForm, is_active: e.target.value === 'active' })}
              >
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={!serverForm.name || !serverForm.hostname || createServerMutation.isPending || updateServerMutation.isPending}
          >
            {editingServer ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteConfirmOpen} onClose={handleDeleteCancel}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the server "{serverToDelete?.name}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={deleteServerMutation.isPending}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
