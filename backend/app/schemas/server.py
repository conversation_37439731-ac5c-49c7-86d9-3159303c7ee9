from pydantic import BaseModel
from typing import Optional
from datetime import datetime

from app.models import ServerType


class ServerBase(BaseModel):
    name: str
    hostname: str
    ip_address: str
    server_type: ServerType
    username: str = "root"
    password: str = "ca$hc0w"


class ServerCreate(ServerBase):
    setup_id: Optional[int] = None


class ServerUpdate(BaseModel):
    name: Optional[str] = None
    hostname: Optional[str] = None
    ip_address: Optional[str] = None
    server_type: Optional[ServerType] = None
    username: Optional[str] = None
    password: Optional[str] = None
    setup_id: Optional[int] = None
    is_active: Optional[bool] = None


class ServerResponse(ServerBase):
    id: int
    setup_id: Optional[int] = None
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True
