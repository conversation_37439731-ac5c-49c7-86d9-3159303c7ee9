from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException
from fastapi import status as http_status
from sqlalchemy.orm import Session
from datetime import datetime
import logging

from app.api import deps
from app.models import Server, TestSetup, ESXiVersionStatus
from app.schemas.server import ServerResponse as ServerSchema, ServerCreate, ServerUpdate
from app.services.esxi_detector import ESXiVersionDetector

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=List[ServerSchema])
def get_servers(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    setup_id: Optional[int] = None
):
    """Get all servers with optional filtering by setup."""
    query = db.query(Server)
    if setup_id is not None:
        query = query.filter(Server.setup_id == setup_id)
    servers = query.offset(skip).limit(limit).all()
    return servers


def _validate_server_uniqueness(db: Session, server_data: dict, exclude_id: Optional[int] = None):
    """Validate that server IP, hostname, and shelf assignments are unique."""
    errors = []

    # Check for duplicate IP address
    ip_query = db.query(Server).filter(Server.ip_address == server_data.get('ip_address'))
    if exclude_id:
        ip_query = ip_query.filter(Server.id != exclude_id)
    if ip_query.first():
        errors.append(f"IP address {server_data.get('ip_address')} is already in use")

    # Check for duplicate hostname
    hostname_query = db.query(Server).filter(Server.hostname == server_data.get('hostname'))
    if exclude_id:
        hostname_query = hostname_query.filter(Server.id != exclude_id)
    if hostname_query.first():
        errors.append(f"Hostname {server_data.get('hostname')} is already in use")

    # Extract shelf number from IP address (assuming format 172.101.{shelf}.{host})
    ip_address = server_data.get('ip_address', '')
    if ip_address.startswith('172.101.'):
        try:
            parts = ip_address.split('.')
            shelf_number = parts[2]
            host_number = parts[3]
            server_type = server_data.get('server_type')

            # Check for shelf conflicts - each shelf should have only one SUT and one AUX
            shelf_query = db.query(Server).filter(
                Server.ip_address.like(f'172.101.{shelf_number}.%'),
                Server.server_type == server_type
            )
            if exclude_id:
                shelf_query = shelf_query.filter(Server.id != exclude_id)

            existing_server = shelf_query.first()
            if existing_server:
                errors.append(f"Shelf {shelf_number} already has a {str(server_type).upper()} server: {existing_server.name}")

            # Validate IP pattern for shelf servers
            expected_host = "11" if server_type == "sut" else "12" if server_type == "aux" else None
            if expected_host and host_number != expected_host:
                errors.append(f"Invalid IP for {str(server_type).upper()} server. Expected 172.101.{shelf_number}.{expected_host}, got {ip_address}")

        except (IndexError, ValueError):
            errors.append(f"Invalid IP address format. Expected 172.101.{{shelf}}.{{host}}, got {ip_address}")

    return errors


@router.post("/", response_model=ServerSchema)
def create_server(
    server: ServerCreate,
    db: Session = Depends(deps.get_db)
):
    """Create a new server."""
    # Validate server uniqueness
    validation_errors = _validate_server_uniqueness(db, server.model_dump())
    if validation_errors:
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail={"message": "Server validation failed", "errors": validation_errors}
        )

    db_server = Server(**server.model_dump())
    db.add(db_server)
    db.commit()
    db.refresh(db_server)
    return db_server


@router.get("/{server_id}", response_model=ServerSchema)
def get_server(
    server_id: int,
    db: Session = Depends(deps.get_db)
):
    """Get a specific server by ID."""
    server = db.query(Server).filter(Server.id == server_id).first()
    if not server:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail=f"Server {server_id} not found"
        )
    return server


@router.put("/{server_id}", response_model=ServerSchema)
def update_server(
    server_id: int,
    server_update: ServerUpdate,
    db: Session = Depends(deps.get_db)
):
    """Update a server."""
    server = db.query(Server).filter(Server.id == server_id).first()
    if not server:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail=f"Server {server_id} not found"
        )

    update_data = server_update.model_dump(exclude_unset=True)

    # Create a merged data dict for validation (existing + updates)
    current_data = {
        'name': server.name,
        'hostname': server.hostname,
        'ip_address': server.ip_address,
        'server_type': server.server_type,
        'username': server.username,
        'password': server.password,
    }
    current_data.update(update_data)

    # Validate server uniqueness (excluding current server)
    validation_errors = _validate_server_uniqueness(db, current_data, exclude_id=server_id)
    if validation_errors:
        raise HTTPException(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            detail={"message": "Server validation failed", "errors": validation_errors}
        )

    # Apply updates
    for field, value in update_data.items():
        setattr(server, field, value)

    db.commit()
    db.refresh(server)
    return server


@router.delete("/{server_id}")
def delete_server(
    server_id: int,
    db: Session = Depends(deps.get_db)
):
    """Delete a server."""
    server = db.query(Server).filter(Server.id == server_id).first()
    if not server:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail=f"Server {server_id} not found"
        )
    
    db.delete(server)
    db.commit()
    return {"detail": f"Server {server_id} deleted"}


@router.post("/{server_id}/check-esxi-version")
def check_server_esxi_version(
    server_id: int,
    db: Session = Depends(deps.get_db)
):
    """Check ESXi version on a server via SSH."""
    server = db.query(Server).filter(Server.id == server_id).first()
    if not server:
        raise HTTPException(
            status_code=http_status.HTTP_404_NOT_FOUND,
            detail=f"Server {server_id} not found"
        )

    try:
        # Detect ESXi version
        detected_version, esxi_status = ESXiVersionDetector.detect_esxi_version(
            str(server.ip_address), str(server.username), str(server.password)
        )

        # Update server with results in database
        setattr(server, 'detected_esxi_version', detected_version)
        setattr(server, 'esxi_version_checked_at', datetime.now())

        # Check compatibility with setup's expected version
        if server.setup_id:
            setup = db.query(TestSetup).filter(TestSetup.id == server.setup_id).first()
            if setup and setup.esxi_version:
                final_status = ESXiVersionDetector.check_version_compatibility(
                    detected_version, setup.esxi_version.value
                )
                setattr(server, 'esxi_version_status', final_status)
            else:
                final_status = esxi_status if esxi_status != ESXiVersionStatus.UNKNOWN else ESXiVersionStatus.UNKNOWN
                setattr(server, 'esxi_version_status', final_status)
        else:
            final_status = esxi_status if esxi_status != ESXiVersionStatus.UNKNOWN else ESXiVersionStatus.UNKNOWN
            setattr(server, 'esxi_version_status', final_status)

        db.commit()
        db.refresh(server)

        return {
            "server_id": server_id,
            "detected_version": detected_version,
            "status": final_status.value,
            "message": f"ESXi version check completed for server {server_id}"
        }

    except Exception as e:
        logger.error(f"Error checking ESXi version for server {server_id}: {e}")
        # Update status to error in database
        setattr(server, 'esxi_version_status', ESXiVersionStatus.ERROR)
        setattr(server, 'esxi_version_checked_at', datetime.now())
        db.commit()

        return {
            "server_id": server_id,
            "detected_version": None,
            "status": ESXiVersionStatus.ERROR.value,
            "error": str(e),
            "message": f"ESXi version check failed for server {server_id}"
        }
