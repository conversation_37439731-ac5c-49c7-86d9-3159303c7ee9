from sqlalchemy import <PERSON>um<PERSON>, Integer, String, DateTime, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, <PERSON>um, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from app.db.base import Base


class SetupStatus(str, enum.Enum):
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    ERROR = "error"


class ServerType(str, enum.Enum):
    SUT = "sut"  # System Under Test
    AUX = "aux"  # Auxiliary


class ESXiVersion(str, enum.Enum):
    ESXI_8_0 = "8.0"
    ESXI_9_0 = "9.0"


class ESXiVersionStatus(str, enum.Enum):
    UNKNOWN = "unknown"
    CHECKING = "checking"
    MATCH = "match"
    MISMATCH = "mismatch"
    ERROR = "error"


class TestSetup(Base):
    __tablename__ = "test_setups"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True)
    description = Column(String, nullable=True)
    status = Column(Enum(SetupStatus), default=SetupStatus.IDLE)
    esxi_version = Column(Enum(ESXiVersion), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    agents = relationship("Agent", back_populates="setup", cascade="all, delete-orphan")
    servers = relationship("Server", back_populates="setup", cascade="all, delete-orphan")
    test_runs = relationship("TestRun", back_populates="setup", cascade="all, delete-orphan")
    runlists = relationship("Runlist", back_populates="setup", cascade="all, delete-orphan")


class Server(Base):
    __tablename__ = "servers"

    id = Column(Integer, primary_key=True, index=True)
    setup_id = Column(Integer, ForeignKey("test_setups.id"), nullable=True)
    name = Column(String)
    hostname = Column(String)
    ip_address = Column(String)
    server_type = Column(Enum(ServerType))
    username = Column(String, default="root")
    password = Column(String, default="ca$hc0w")
    is_active = Column(Boolean, default=True)
    detected_esxi_version = Column(String, nullable=True)
    esxi_version_status = Column(Enum(ESXiVersionStatus), default=ESXiVersionStatus.UNKNOWN)
    esxi_version_checked_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    setup = relationship("TestSetup", back_populates="servers")


class Agent(Base):
    __tablename__ = "agents"
    
    id = Column(Integer, primary_key=True, index=True)
    setup_id = Column(Integer, ForeignKey("test_setups.id"), nullable=True)
    name = Column(String)
    ip_address = Column(String)
    port = Column(Integer, default=22)
    username = Column(String, default="root")
    password = Column(String, default="vmware")
    is_primary = Column(Boolean, default=True)
    is_active = Column(Boolean, default=True)
    last_seen = Column(DateTime(timezone=True), nullable=True)
    capabilities = Column(JSON, default=dict)
    
    setup = relationship("TestSetup", back_populates="agents")
    test_executions = relationship("TestExecution", back_populates="agent")