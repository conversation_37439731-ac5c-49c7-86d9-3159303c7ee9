from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

from app.models import SetupStatus, ESXiVersion


class SetupBase(BaseModel):
    name: str
    description: Optional[str] = None
    esxi_version: Optional[ESXiVersion] = None


class SetupCreate(SetupBase):
    pass


class SetupUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[SetupStatus] = None
    esxi_version: Optional[ESXiVersion] = None


class SetupListResponse(SetupBase):
    id: int
    status: SetupStatus
    created_at: datetime

    class Config:
        from_attributes = True


class SetupResponse(SetupListResponse):
    updated_at: Optional[datetime] = None
    agents: List["AgentResponse"] = []
    servers: List["ServerResponse"] = []


from app.schemas.agent import AgentResponse
from app.schemas.server import ServerResponse
SetupResponse.model_rebuild()