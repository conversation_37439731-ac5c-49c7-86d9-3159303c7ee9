"""ESXi version detection service."""

import re
import logging
from typing import Op<PERSON>, <PERSON><PERSON>
from datetime import datetime

from app.services.ssh_executor import SSHExecutor
from app.models import ESXiVersionStatus

logger = logging.getLogger(__name__)


class ESXiVersionDetector:
    """Service for detecting ESXi versions on servers via SSH."""
    
    @staticmethod
    def detect_esxi_version(hostname: str, username: str = "root", password: str = "ca$hc0w", port: int = 22) -> Tuple[Optional[str], ESXiVersionStatus]:
        """
        Detect ESXi version on a server via SSH.
        
        Args:
            hostname: Server hostname or IP address
            username: SSH username
            password: SSH password
            port: SSH port
            
        Returns:
            Tuple of (detected_version, status)
        """
        try:
            with SSHExecutor(hostname, username, password, port) as ssh:
                # Try to get ESXi version using vmware -v command
                stdout, stderr, exit_code = ssh.execute_command("vmware -v", timeout=30)
                
                if exit_code != 0:
                    logger.warning(f"Failed to execute 'vmware -v' on {hostname}: {stderr}")
                    return None, ESXiVersionStatus.ERROR
                
                # Parse version from output
                # Expected format: "VMware ESXi 8.0.0 build-20513097"
                version = ESXiVersionDetector._parse_esxi_version(stdout)
                
                if version:
                    logger.info(f"Detected ESXi version {version} on {hostname}")
                    return version, ESXiVersionStatus.UNKNOWN  # Will be set to MATCH/MISMATCH by caller
                else:
                    logger.warning(f"Could not parse ESXi version from output on {hostname}: {stdout}")
                    return None, ESXiVersionStatus.ERROR
                    
        except Exception as e:
            logger.error(f"Error detecting ESXi version on {hostname}: {e}")
            return None, ESXiVersionStatus.ERROR
    
    @staticmethod
    def _parse_esxi_version(vmware_output: str) -> Optional[str]:
        """
        Parse ESXi version from 'vmware -v' command output.
        
        Args:
            vmware_output: Output from 'vmware -v' command
            
        Returns:
            Parsed version string (e.g., "8.0", "9.0") or None if not found
        """
        # Look for patterns like "ESXi 8.0.0", "ESXi 9.0.0", etc.
        patterns = [
            r'ESXi\s+(\d+\.\d+)',  # "ESXi 8.0"
            r'VMware ESXi\s+(\d+\.\d+)',  # "VMware ESXi 8.0"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, vmware_output, re.IGNORECASE)
            if match:
                version = match.group(1)
                # Normalize to major.minor format
                if version.startswith('8.'):
                    return "8.0"
                elif version.startswith('9.'):
                    return "9.0"
                else:
                    return version
        
        return None
    
    @staticmethod
    def check_version_compatibility(detected_version: Optional[str], expected_version: Optional[str]) -> ESXiVersionStatus:
        """
        Check if detected ESXi version matches expected version.
        
        Args:
            detected_version: Version detected on server
            expected_version: Expected version from setup
            
        Returns:
            ESXiVersionStatus indicating compatibility
        """
        if not detected_version:
            return ESXiVersionStatus.ERROR
        
        if not expected_version:
            return ESXiVersionStatus.UNKNOWN
        
        # Normalize versions for comparison
        detected_normalized = detected_version.strip()
        expected_normalized = expected_version.strip()
        
        if detected_normalized == expected_normalized:
            return ESXiVersionStatus.MATCH
        else:
            return ESXiVersionStatus.MISMATCH
